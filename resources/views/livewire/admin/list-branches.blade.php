<div>
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Branch Management</h2>
                        <p class="mt-2 mb-0 text-muted">Manage cooperative branches and their approval status</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Statistics Cards --}}
    <div class="mb-4 row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-primary">
                            <i class="ph-duotone ph-buildings"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Branches</p>
                            <h4 class="mb-0 fw-bold">{{ $branchesSummary->sum('total') }}</h4>
                            <small class="text-primary">All registered branches</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-warning">
                            <i class="ph-duotone ph-clock"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Pending Approval</p>
                            <h4 class="mb-0 fw-bold">{{ $branchesSummary->sum('pending') }}</h4>
                            <small class="text-warning">Awaiting verification</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-success">
                            <i class="ph-duotone ph-check-circle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Active Branches</p>
                            <h4 class="mb-0 fw-bold">{{ $branchesSummary->sum('active') }}</h4>
                            <small class="text-success">Approved & operational</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-danger">
                            <i class="ph-duotone ph-x-circle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Inactive Branches</p>
                            <h4 class="mb-0 fw-bold">{{ $branchesSummary->sum('inactive') }}</h4>
                            <small class="text-danger">Declined or suspended</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Cooperative Summary Card --}}
    <div class="mb-4 card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">Branch Summary by Cooperative</h2>
                </div>
                <div class="page-header-breadcrumb">
                    <button class="btn btn-outline-secondary" type="button" onclick="toggleSummaryDetails()" id="summaryToggleBtn">
                        <i class="ph-duotone ph-caret-down" id="summaryChevron"></i>
                        <span class="ms-1">Show Details</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body" id="summaryDetails" style="display: none;">
            <div class="row">
                @foreach($branchesSummary as $summary)
                <div class="mb-3 col-lg-6 col-xl-4">
                    <div class="border card">
                        <div class="p-3 card-body">
                            <div class="mb-2 d-flex align-items-center">
                                <div class="avtar avtar-s bg-light-info me-2">
                                    <i class="ph-duotone ph-buildings"></i>
                                </div>
                                <h6 class="mb-0 card-title">{{ $summary->cooperative_name }}</h6>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Total: {{ $summary->total }}</small>
                                <div>
                                    @if($summary->pending > 0)
                                        <span class="badge bg-warning me-1">{{ $summary->pending }}</span>
                                    @endif
                                    @if($summary->active > 0)
                                        <span class="badge bg-success me-1">{{ $summary->active }}</span>
                                    @endif
                                    @if($summary->inactive > 0)
                                        <span class="badge bg-danger">{{ $summary->inactive }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">Branch Directory</h2>
                    @if ($branches->total() > 0)
                        <p class="mt-1 mb-0 text-muted">Showing {{ $branches->firstItem() }} to
                            {{ $branches->lastItem() }} of {{ $branches->total() }} results</p>
                    @endif
                </div>
                <div class="page-header-breadcrumb">
                    <button wire:click="toggleFilters" class="btn btn-outline-secondary me-2">
                        <i class="ph-duotone ph-funnel me-2"></i>
                        {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                    </button>
                </div>
            </div>
        </div>

        {{-- Advanced Filters Panel --}}
        @if ($showFilters)
            <div class="card-body border-bottom">
                <div>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Search Branches</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ph-duotone ph-magnifying-glass"></i>
                                </span>
                                <input type="text" wire:model.live="search" class="form-control" placeholder="Search branches...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Cooperative</label>
                            <select wire:model.live="selectedCooperative" class="form-select">
                                <option value="">All Cooperatives</option>
                                @foreach($cooperatives as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Status Filter</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="status" id="all" wire:model.live="selectedStatus" value="" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="all">All</label>
                                <input type="radio" class="btn-check" name="status" id="pending" wire:model.live="selectedStatus" value="pending" autocomplete="off">
                                <label class="btn btn-outline-warning" for="pending">Pending</label>
                                <input type="radio" class="btn-check" name="status" id="active" wire:model.live="selectedStatus" value="active" autocomplete="off">
                                <label class="btn btn-outline-success" for="active">Active</label>
                                <input type="radio" class="btn-check" name="status" id="inactive" wire:model.live="selectedStatus" value="inactive" autocomplete="off">
                                <label class="btn btn-outline-danger" for="inactive">Inactive</label>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Cooperative</th>
                                    <th>Status</th>
                                    <th>Created By</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($branches as $branch)
                                <tr>
                                    <td>{{ $branch->name }}</td>
                                    <td>{{ $branch->cooperative->name ?? 'N/A' }}</td>
                                    <td>
                                        @if($branch->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($branch->status === 'active')
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $branch->creator->name ?? 'N/A' }}</td>
                                    <td>{{ $branch->created_at->format('d M Y') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button"
                                                    class="btn btn-sm btn-info"
                                                    wire:click="showBranchDetails('{{ $branch->id }}')"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if($branch->status === 'pending')
                                            <button type="button"
                                                    class="btn btn-sm btn-success"
                                                    wire:click="openApproval({{ $branch->id }})"
                                                    wire:loading.attr="disabled"
                                                    title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-sm btn-danger"
                                                    wire:click="openRejection({{ $branch->id }})"
                                                    wire:loading.attr="disabled"
                                                    title="Decline"
                                                    onclick="console.log('Decline button clicked for branch {{ $branch->id }}')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No branches found.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif

    <!-- Branch Details Modal -->
    @if($showModal && $selectedBranch)
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog" wire:click="closeModal">
        <div class="modal-dialog modal-lg" role="document" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Branch Details</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
            </div>

            {{-- Branch Table --}}
            <div class="table-responsive" style="overflow: visible;">
                <table class="table table-hover" id="branchTable">
                    <thead>
                        <tr>
                            <th>
                                <div class="d-flex align-items-center">
                                    Branch Name
                                    <i class="ph-duotone ph-buildings ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Cooperative
                                    <i class="ph-duotone ph-network ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Status
                                    <i class="ph-duotone ph-info ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Created Date
                                    <i class="ph-duotone ph-calendar ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($branches as $branch)
                            <tr id="branch-row-{{ $branch->id }}" class="align-middle">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avtar avtar-s bg-light-primary">
                                            <i class="ph-duotone ph-buildings"></i>
                                        </div>
                                        <div class="ms-2">
                                            <h6 class="mb-0">{{ $branch->name }}</h6>
                                            <small class="text-muted">Branch location</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avtar avtar-s bg-light-info">
                                            <i class="ph-duotone ph-network"></i>
                                        </div>
                                        <div class="ms-2">
                                            <h6 class="mb-0">{{ $branch->cooperative->name ?? 'N/A' }}</h6>
                                            <small class="text-muted">Parent cooperative</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($branch->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($branch->status === 'active')
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-danger">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="fw-medium">{{ $branch->created_at->format('M d, Y') }}</span>
                                    <small class="text-muted d-block">{{ $branch->created_at->format('H:i') }}</small>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="ph-duotone ph-dots-three-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <button wire:click="showBranchDetails('{{ $branch->id }}')"
                                                    class="dropdown-item">
                                                    <i class="ph-duotone ph-eye me-2"></i>View Details
                                                </button>
                                            </li>
                                            @if($branch->status === 'pending')
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button wire:click="approveBranch('{{ $branch->id }}')"
                                                        wire:loading.attr="disabled"
                                                        class="dropdown-item text-success">
                                                        <i class="ph-duotone ph-check me-2"></i>Approve
                                                    </button>
                                                </li>
                                                <li>
                                                    <button wire:click="declineBranch('{{ $branch->id }}')"
                                                        wire:loading.attr="disabled"
                                                        class="dropdown-item text-danger">
                                                        <i class="ph-duotone ph-x me-2"></i>Decline
                                                    </button>
                                                </li>
                                            @endif
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="py-4 text-center">
                                    <div class="py-4 text-center">
                                        <div class="mx-auto mb-3 avtar avtar-xl bg-light-secondary">
                                            <i class="ph-duotone ph-buildings"></i>
                                        </div>
                                        <h5>No Branches Found</h5>
                                        <p class="text-muted">No branches match the current search criteria or filters.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            @if ($branches->hasPages())
                <div class="mt-4 d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        Showing {{ $branches->firstItem() }} to {{ $branches->lastItem() }} of
                        {{ $branches->total() }} results
                    </div>
                    <div>
                        {{ $branches->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    {{-- Branch Details Modal --}}
    @if($showModal && $selectedBranch)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="border-0 modal-content" style="border-radius: 16px;">
                    <div class="pb-0 border-0 modal-header">
                        <button wire:click="closeModal" type="button" class="btn-close"
                            style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="px-4 py-5 modal-body">
                        <div class="mb-4 text-center">
                            <div class="mx-auto mb-4"
                                style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-buildings" style="font-size: 3rem; color: #1976d2;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Branch Details</h4>
                            <p class="mb-4 text-muted">Detailed information about {{ $selectedBranch->name }}</p>
                        </div>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-buildings me-2"></i>Branch Name
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span class="fw-medium">{{ $selectedBranch->name }}</span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-identification-card me-2"></i>Business Registration No
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span>{{ $selectedBranch->business_registration_no ?? 'N/A' }}</span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-network me-2"></i>Cooperative
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span>{{ $selectedBranch->cooperative->name ?? 'N/A' }}</span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-info me-2"></i>Status
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        @if($selectedBranch->status === 'pending')
                                            <span class="badge bg-warning fs-6">Pending Approval</span>
                                        @elseif($selectedBranch->status === 'active')
                                            <span class="badge bg-success fs-6">Active</span>
                                        @else
                                            <span class="badge bg-danger fs-6">Inactive</span>
                                        @endif
                                    </div>
                                </div>

                                @if($selectedBranch->status === 'active' && $selectedBranch->registration_token)
                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-key me-2"></i>Registration Token
                                    </label>
                                    <div class="input-group">
                                        <input type="text"
                                               class="form-control"
                                               value="{{ $selectedBranch->registration_token }}"
                                               readonly
                                               id="registration-token-{{ $selectedBranch->id }}"
                                               style="border-radius: 10px 0 0 10px;">
                                        <button class="btn btn-outline-primary"
                                                type="button"
                                                onclick="copyToClipboard('registration-token-{{ $selectedBranch->id }}')"
                                                title="Copy to clipboard"
                                                style="border-radius: 0 10px 10px 0;">
                                            <i class="ph-duotone ph-copy"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">This token is generated when the branch is approved and can be used for registration purposes.</small>
                                </div>
                                @endif
                            </div>

                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-map-pin me-2"></i>Address
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span>{{ $selectedBranch->address ?? 'N/A' }}</span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-user me-2"></i>Created By
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span>{{ $selectedBranch->creator->name ?? 'N/A' }}</span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-calendar-plus me-2"></i>Created At
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span>{{ $selectedBranch->created_at->format('M d, Y H:i:s') }}</span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-user-check me-2"></i>Verified By
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span>{{ $selectedBranch->verifier->name ?? 'N/A' }}</span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-semibold text-dark d-flex align-items-center">
                                        <i class="ph-duotone ph-calendar-check me-2"></i>Verified At
                                    </label>
                                    <div class="p-3 rounded bg-light">
                                        <span>{{ $selectedBranch->verified_at ? $selectedBranch->verified_at->format('M d, Y H:i:s') : 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 d-flex justify-content-center">
                            <button wire:click="closeModal" type="button" class="px-4 btn btn-primary"
                                style="border-radius: 10px; font-weight: 500;">
                                <i class="ph-duotone ph-x me-2"></i>Close Details
                            </button>
                        </div>
                    </div>

                    <!-- Approval History Section -->
                    @if($selectedBranch->approvalHistory && $selectedBranch->approvalHistory->count() > 0)
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">Approval History</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Action</th>
                                            <th>By</th>
                                            <th>Reason</th>
                                            <th>Status Change</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($selectedBranch->approvalHistory->sortByDesc('created_at') as $history)
                                        <tr>
                                            <td>{{ $history->created_at->format('d M Y H:i:s') }}</td>
                                            <td>
                                                @if($history->action === 'approve')
                                                    <span class="badge bg-success">{{ ucfirst($history->action) }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ ucfirst($history->action) }}</span>
                                                @endif
                                            </td>
                                            <td>{{ $history->actionBy->name ?? 'N/A' }}</td>
                                            <td>
                                                @if($history->reason)
                                                    <span class="text-muted">{{ Str::limit($history->reason, 50) }}</span>
                                                @else
                                                    <span class="text-muted">No reason provided</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ ucfirst($history->previous_status) }} → {{ ucfirst($history->new_status) }}
                                                </small>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Approval Modal -->
    @if($showApprovalModal && $pendingActionBranch)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1" wire:click="closeApprovalModal">
        <div class="modal-dialog" role="document" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Approve Branch</h5>
                    <button type="button" class="btn-close" wire:click="closeApprovalModal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to approve the branch <strong>{{ $pendingActionBranch->name }}</strong>?</p>

                    <div class="mb-3">
                        <label for="approvalReason" class="form-label">Approval Reason (Optional)</label>
                        <textarea
                            wire:model="approvalReason"
                            class="form-control"
                            id="approvalReason"
                            rows="3"
                            placeholder="Enter approval reason (optional)..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeApprovalModal">Cancel</button>
                    <button type="button"
                            class="btn btn-success"
                            wire:click="approveBranch('{{ $pendingActionBranch->id }}')"
                            wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="approveBranch('{{ $pendingActionBranch->id }}')">
                            Approve Branch
                        </span>
                        <span wire:loading wire:target="approveBranch('{{ $pendingActionBranch->id }}')">
                            Approving...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif

    <!-- Rejection Modal -->
    <!-- Debug: showRejectionModal = {{ $showRejectionModal ? 'true' : 'false' }}, pendingActionBranch = {{ $pendingActionBranch ? 'exists' : 'null' }} -->
    @if($showRejectionModal && $pendingActionBranch)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4); z-index: 1050;" tabindex="-1" wire:click="closeRejectionModal">
        <div class="modal-dialog" role="document" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Decline Branch</h5>
                    <button type="button" class="btn-close" wire:click="closeRejectionModal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to decline the branch <strong>{{ $pendingActionBranch->name }}</strong>?</p>

                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea
                            wire:model="rejectionReason"
                            class="form-control"
                            id="rejectionReason"
                            rows="3"
                            placeholder="Please provide a reason for rejection..."
                            required></textarea>
                        @error('rejectionReason')
                            <div class="mt-1 text-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeRejectionModal">Cancel</button>
                    <button type="button"
                            class="btn btn-danger"
                            wire:click="declineBranch('{{ $pendingActionBranch->id }}')"
                            wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="declineBranch('{{ $pendingActionBranch->id }}')">
                            Decline Branch
                        </span>
                        <span wire:loading wire:target="declineBranch('{{ $pendingActionBranch->id }}')">
                            Declining...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show" style="z-index: 1040;"></div>
    @endif

    <script>
        // Add keyboard event listener for Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // Check if any modal is open and close it
                if (document.querySelector('.modal.show')) {
                    // Trigger the appropriate close method based on which modal is open
                    if (document.querySelector('[wire\\:click="closeModal"]')) {
                        @this.closeModal();
                    } else if (document.querySelector('[wire\\:click="closeApprovalModal"]')) {
                        @this.closeApprovalModal();
                    } else if (document.querySelector('[wire\\:click="closeRejectionModal"]')) {
                        @this.closeRejectionModal();
                    }
                }
            }
        });

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');
                // Show success message
                const button = element.nextElementSibling;
                const originalIcon = button.innerHTML;
                button.innerHTML = '<i class="ph-duotone ph-check"></i>';
                button.classList.remove('btn-outline-primary');
                button.classList.add('btn-success');

                setTimeout(() => {
                    button.innerHTML = originalIcon;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-primary');
                }, 2000);
            } catch (err) {
                console.error('Failed to copy: ', err);
            }
        }

        function toggleSummaryDetails() {
            const details = document.getElementById('summaryDetails');
            const chevron = document.getElementById('summaryChevron');
            const toggleBtn = document.getElementById('summaryToggleBtn');

            if (details.style.display === 'none') {
                details.style.display = 'block';
                chevron.classList.remove('ph-caret-down');
                chevron.classList.add('ph-caret-up');
                toggleBtn.querySelector('span').textContent = 'Hide Details';
            } else {
                details.style.display = 'none';
                chevron.classList.remove('ph-caret-up');
                chevron.classList.add('ph-caret-down');
                toggleBtn.querySelector('span').textContent = 'Show Details';
            }
        }
    </script>
</div>
