<div>
    <style>
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: visible !important;
        }

        .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }

        .dropdown {
            position: static !important;
        }

        .card {
            overflow: visible !important;
        }
    </style>
    
    {{-- <PERSON> Header --}}
    <div class="page-header commission-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Commission Distribution Reports</h2>
                        <p class="text-muted mb-0 mt-2">Track and analyze commission distribution across agents and campaigns</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Statistics Cards --}}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-primary">
                            <i class="ph-duotone ph-chart-pie"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Total Distributions</p>
                            <h4 class="mb-0 fw-bold">{{ number_format($stats['total_distributions']) }}</h4>
                            <small class="text-primary">Commission records</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-success">
                            <i class="ph-duotone ph-currency-circle-dollar"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Total Commission</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($stats['total_commission_amount'], 2) }}</h4>
                            <small class="text-success">All distributions</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-info">
                            <i class="ph-duotone ph-users"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Main Agent Commission</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($stats['total_main_agent_commission'], 2) }}</h4>
                            <small class="text-info">Main agents total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-warning">
                            <i class="ph-duotone ph-user"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Agent Commission</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($stats['total_agent_commission'], 2) }}</h4>
                            <small class="text-warning">Agents total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">Commission Distribution Records</h2>
                    @if ($distributions->total() > 0)
                        <p class="text-muted mb-0 mt-1">Showing {{ $distributions->firstItem() }} to
                            {{ $distributions->lastItem() }} of {{ $distributions->total() }} results</p>
                    @endif
                </div>
                <div class="page-header-breadcrumb">
                    <button wire:click="toggleFilters" class="btn btn-outline-secondary me-2">
                        <i class="ph-duotone ph-funnel me-2"></i>
                        {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                    </button>
                    <button wire:click="clearFilters" class="btn btn-outline-primary">
                        <i class="ph-duotone ph-arrow-clockwise me-2"></i>Clear Filters
                    </button>
                </div>
            </div>
        </div>

        {{-- Advanced Filters Panel --}}
        @if ($showFilters)
            <div class="card-body border-bottom">
                <div class="bg-light p-4" style="border-radius: 12px;">
                    <h5 class="mb-3 fw-semibold">
                        <i class="ph-duotone ph-funnel me-2"></i>Advanced Filters
                    </h5>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Cooperative</label>
                            <select wire:model.live="cooperative_id" class="form-select">
                                <option value="">All Cooperatives</option>
                                @foreach($cooperatives as $cooperative)
                                    <option value="{{ $cooperative->id }}">{{ $cooperative->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Branch</label>
                            <select wire:model.live="branch_id" class="form-select">
                                <option value="">All Branches</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Main Agent</label>
                            <select wire:model.live="main_agent_id" class="form-select">
                                <option value="">All Main Agents</option>
                                @foreach($mainAgents as $mainAgent)
                                    <option value="{{ $mainAgent->id }}">{{ $mainAgent->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Agent</label>
                            <select wire:model.live="agent_id" class="form-select">
                                <option value="">All Agents</option>
                                @foreach($agents as $agent)
                                    <option value="{{ $agent->id }}">{{ $agent->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" wire:model.live="date_from" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" wire:model.live="date_to" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Min Amount (RM)</label>
                            <input type="number" step="0.01" wire:model.live="min_amount" class="form-control" placeholder="0.00">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Max Amount (RM)</label>
                            <input type="number" step="0.01" wire:model.live="max_amount" class="form-control" placeholder="0.00">
                        </div>
                    </div>
                </div>
            </div>
        @endif

        {{-- Data Table --}}
        <div class="card-body">
            @if ($distributions->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <button wire:click="sortBy('id')" class="btn btn-link p-0 text-dark fw-semibold">
                                        ID
                                        @if($sortBy === 'id')
                                            <i class="ph-duotone ph-caret-{{ $sortOrder === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </button>
                                </th>
                                <th>Cooperative / Branch</th>
                                <th>Main Agent</th>
                                <th>Agent</th>
                                <th>
                                    <button wire:click="sortBy('main_agent_commission_amount')" class="btn btn-link p-0 text-dark fw-semibold">
                                        Main Agent Commission
                                        @if($sortBy === 'main_agent_commission_amount')
                                            <i class="ph-duotone ph-caret-{{ $sortOrder === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </button>
                                </th>
                                <th>
                                    <button wire:click="sortBy('agent_commission_amount')" class="btn btn-link p-0 text-dark fw-semibold">
                                        Agent Commission
                                        @if($sortBy === 'agent_commission_amount')
                                            <i class="ph-duotone ph-caret-{{ $sortOrder === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </button>
                                </th>
                                <th>Total Commission</th>
                                <th>Campaign</th>
                                <th>
                                    <button wire:click="sortBy('created_at')" class="btn btn-link p-0 text-dark fw-semibold">
                                        Date Created
                                        @if($sortBy === 'created_at')
                                            <i class="ph-duotone ph-caret-{{ $sortOrder === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </button>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($distributions as $distribution)
                                <tr>
                                    <td>
                                        <span class="badge bg-light-secondary">{{ $distribution->id }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-semibold">{{ $distribution->cooperative->name ?? 'N/A' }}</div>
                                            <small class="text-muted">{{ $distribution->cooperativeBranch->name ?? 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        @if($distribution->mainAgent)
                                            <div>
                                                <div class="fw-semibold">{{ $distribution->mainAgent->name }}</div>
                                                <small class="text-muted">{{ $distribution->mainAgent->email }}</small>
                                            </div>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($distribution->agent)
                                            <div>
                                                <div class="fw-semibold">{{ $distribution->agent->name }}</div>
                                                <small class="text-muted">{{ $distribution->agent->email }}</small>
                                            </div>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="text-success fw-semibold">
                                            RM {{ number_format($distribution->main_agent_commission_amount, 2) }}
                                        </div>
                                        <small class="text-muted">{{ number_format($distribution->main_agent_percentage, 2) }}%</small>
                                    </td>
                                    <td>
                                        <div class="text-info fw-semibold">
                                            RM {{ number_format($distribution->agent_commission_amount, 2) }}
                                        </div>
                                        <small class="text-muted">{{ number_format($distribution->agent_percentage, 2) }}%</small>
                                    </td>
                                    <td>
                                        <div class="text-primary fw-bold">
                                            RM {{ number_format($distribution->total_commission_amount, 2) }}
                                        </div>
                                    </td>
                                    <td>
                                        @if($distribution->salesCommission && $distribution->salesCommission->campaign)
                                            <span class="badge bg-light-primary">
                                                {{ $distribution->salesCommission->campaign->campaign_name }}
                                            </span>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $distribution->created_at->format('d M Y') }}</div>
                                        <small class="text-muted">{{ $distribution->created_at->format('H:i') }}</small>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                {{-- Pagination Controls --}}
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="d-flex align-items-center">
                        <label class="form-label me-2 mb-0">Show:</label>
                        <select wire:model.live="perPage" class="form-select form-select-sm" style="width: auto;">
                            <option value="10">10</option>
                            <option value="15">15</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                        <span class="ms-2 text-muted">entries per page</span>
                    </div>
                    <div>
                        {{ $distributions->links() }}
                    </div>
                </div>
            @else
                {{-- Empty State --}}
                <div class="text-center py-5">
                    <div class="avtar avtar-xl bg-light-secondary mx-auto mb-4" style="border-radius: 16px;">
                        <i class="ph-duotone ph-chart-pie text-secondary" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-3">No Commission Distributions Found</h4>
                    <p class="text-muted mb-4">
                        @if($showFilters && ($cooperative_id || $branch_id || $main_agent_id || $agent_id || $date_from || $date_to || $min_amount || $max_amount))
                            No distributions match your current filter criteria.<br>
                            Try adjusting your filters or clear them to see all distributions.
                        @else
                            There are no commission distributions to display at the moment.<br>
                            Distributions will appear here once sales commissions are processed.
                        @endif
                    </p>
                    @if($showFilters)
                        <button wire:click="clearFilters" class="btn btn-primary">
                            <i class="ph-duotone ph-arrow-clockwise me-2"></i>Clear All Filters
                        </button>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>